package moat

import (
	"errors"
	"net/url"
	"reflect"
	"sort"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

const (
	AppKey    = "assistantdesk"
	AppSecret = "86ec2d3211ac6e7ff5fcbd95fbf4e2c0"
)

// 合成具有Sign的params  生成sign + 取出appSecret参数
func SignParams(ctx *gin.Context, params map[string]interface{}) (map[string]interface{}, error) {
	// 计算签名
	appKey, ok1 := params["appKey"]
	appSecret, ok2 := params["appSecret"]
	if !ok1 || !ok2 {
		return nil, errors.New("appKey or appSecret err")
	}
	appKeyStr, ok1 := appKey.(string)
	appSecretStr, ok2 := appSecret.(string)
	if !ok1 || !ok2 {
		return nil, errors.New("appKey or appSecret type err")
	}
	delete(params, "appSecret")
	paramsStr := EncodeParamMap(ctx, params)
	paramsStr["sign"] = GetSign(appKeyStr, appSecretStr, paramsStr)

	return paramsStr, nil
}

// 将map[string]interface{}结构的param转换为map[string]string
func EncodeParamMap(ctx *gin.Context, maps map[string]interface{}) map[string]interface{} {
	res := make(map[string]interface{})
	for k, v := range maps {
		if reflect.TypeOf(v).Kind() == reflect.String {
			value, ok := v.(string)
			if !ok {
				zlog.Errorf(ctx, "request_params_error: key = ", k, " response: ", v)
				continue
			}
			res[k] = value
		} else {
			str, err := jsoniter.Marshal(v)
			if err == nil {
				res[k] = string(str)
			}
		}
	}
	return res
}

func GetSign(appKey, appSecret string, params map[string]interface{}) string {
	var keys sort.StringSlice
	for k := range params {
		keys = append(keys, k)
	}
	sort.Sort(keys)

	var tempSign strings.Builder
	tempSign.WriteString(appKey)
	for _, k := range keys {
		v := params[k].(string)

		k = strings.Trim(k, " ")
		v = strings.Trim(v, " ")
		if v == "" || k == "sign" {
			continue
		}

		// url解密
		v, _ = url.QueryUnescape(v)

		tempSign.WriteString(k)
		tempSign.WriteString(v)
	}
	tempSign.WriteString(appSecret)

	signStr := tempSign.String()

	//log.Warnf("ppppp_signStr:%s",signStr)
	return strings.ToLower(utils.Md5(signStr))
}
