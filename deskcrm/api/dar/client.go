package dar

import (
	"deskcrm/api/moat"
	"deskcrm/api/zbcore"
	"deskcrm/conf"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// TradeInfo 订单信息结构体
type TradeInfo struct {
	StudentUid         int64  `json:"studentUid"`         // 学生UID
	CourseId           int64  `json:"courseId"`           // 课程ID
	TradeId            int64  `json:"tradeId"`            // 主订单ID
	SubTradeId         int64  `json:"subTradeId"`         // 子订单ID
	TradeFee           int64  `json:"tradeFee"`           // 订单实际支付金额
	TradeTime          int64  `json:"tradeTime"`          // 支付回调时间(报名时间)
	Status             int    `json:"status"`             // 订单状态
	RefundStartTime    int64  `json:"refundStartTime"`    // 退款开始时间
	ChangeTime         int64  `json:"changeTime"`         // 最近一次调课时间
	ChangeFromCourseId int64  `json:"changeFromCourseId"` // 从哪个课程调课过来
	ChangeToCourseId   int64  `json:"changeToCourseId"`   // 调到那个课程
	CreateTime         int64  `json:"createTime"`         // 创建时间
	UpdateTime         int64  `json:"updateTime"`         // 更新时间
	LogInfo            string `json:"logInfo"`            // 日志信息
}

// TradeStatus 订单状态常量
const (
	TradeStatusUnknown       = -1 // 未知状态
	TradeStatusUnpaid        = 0  // 待支付
	TradeStatusPaid          = 1  // 已支付
	TradeStatusRefunded      = 2  // 已退款
	TradeStatusRefundPart    = 21 // 部分退款
	TradeStatusRefunding     = 3  // 退款中
	TradeStatusBound         = 4  // 预约订单绑定状态
	TradeStatusClosed        = 5  // 已关闭
	TradeStatusHolding       = 6  // 代扣预约
	TradeStatusHoldingCancel = 7  // 取消代扣预约
	TradeStatusRefundPause   = 8  // 退款暂停
)

// OrderBusinessStatus 订单业务状态常量
const (
	OrderBusinessStatusUnpaid     = 0 // 未支付
	OrderBusinessStatusPaid       = 1 // 已支付
	OrderBusinessStatusFulfilling = 2 // 履约中
	OrderBusinessStatusToProduce  = 3 // 待发货
	OrderBusinessStatusReceive    = 4 // 待收货
	OrderBusinessStatusFinished   = 5 // 已完成
	OrderBusinessStatusPause      = 7 // 暂停
	OrderBusinessStatusPartPaid   = 8 // 部分支付
)

// RefundStatus 售后状态常量
const (
	RefundStatusRefunded = 1 // 已退款
)

// GetTradeKVByCourseIds 根据学生课程ID获取交易信息
func GetTradeKVByCourseIds(ctx *gin.Context, studentCourseIds []string) (map[string]*TradeInfo, error) {
	if len(studentCourseIds) == 0 {
		return make(map[string]*TradeInfo), nil
	}

	// 调用 zbcore DAR 服务
	fields := []string{
		"userId", "courseId", "tradeId", "subTradeId", "tradeFee", "tradeTime",
		"orderBusinessStatus", "refundStatus", "refundStartTime", "changeFromCourseId",
		"changeToCourseId", "changeTime", "createTime", "updateTime", "logInfo",
	}

	input := map[string]interface{}{
		"studentCourseIds": studentCourseIds,
		"fields":           fields,
		"orderChannel":     []int{0},
		"appKey":           moat.AppKey,
		"appSecret":        moat.AppSecret,
	}

	moatParams, signErr := moat.SignParams(ctx, input)
	if signErr != nil {
		return nil, signErr
	}

	output := map[string]interface{}{}

	// 构建请求头
	headers := zbcore.GetHeaders("/zbcore/api/api", "dar", "subTrade", "getKVByCourseIds", true, conf.GetAppName())

	// 调用 DAR 服务
	resp, err := zbcore.PostDar(ctx, moatParams, headers, &output)
	if err != nil {
		zlog.Errorf(ctx, "GetTradeKVByCourseIds call failed, err: %v", err)
		return nil, err
	}

	if resp == nil || resp.ErrNo != zbcore.Success {
		zlog.Errorf(ctx, "GetTradeKVByCourseIds api failed, errNo: %d, errMsg: %s", resp.ErrNo, resp.ErrStr)
		return nil, fmt.Errorf("api failed: %s", resp.ErrStr)
	}

	// 解析响应数据
	return parseTradeResponse(ctx, output)
}

// parseTradeResponse 解析交易响应数据
func parseTradeResponse(ctx *gin.Context, output map[string]interface{}) (map[string]*TradeInfo, error) {
	result := make(map[string]*TradeInfo)

	// 获取data字段
	dataInterface, exists := output["data"]
	if !exists {
		zlog.Warnf(ctx, "parseTradeResponse: no data field in response")
		return result, nil
	}

	// 转换为数组
	dataArray, ok := dataInterface.([]interface{})
	if !ok {
		zlog.Warnf(ctx, "parseTradeResponse: data field is not array, type: %T", dataInterface)
		return result, nil
	}

	// 遍历处理每个订单记录
	for _, item := range dataArray {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			zlog.Warnf(ctx, "parseTradeResponse: item is not map, type: %T", item)
			continue
		}

		// 解析订单信息
		tradeInfo, err := parseTradeItem(ctx, itemMap)
		if err != nil {
			zlog.Warnf(ctx, "parseTradeResponse: parse trade item failed, err: %v", err)
			continue
		}

		// 过滤未知状态的订单
		if tradeInfo.Status == TradeStatusUnknown {
			continue
		}

		// 构建key: studentUid_courseId
		key := fmt.Sprintf("%d_%d", tradeInfo.StudentUid, tradeInfo.CourseId)
		result[key] = tradeInfo
	}

	return result, nil
}

// parseTradeItem 解析单个订单项
func parseTradeItem(ctx *gin.Context, itemMap map[string]interface{}) (*TradeInfo, error) {
	tradeInfo := &TradeInfo{}

	// 解析基础字段
	if userId, ok := itemMap["userId"].(float64); ok {
		tradeInfo.StudentUid = int64(userId)
	}
	if courseId, ok := itemMap["courseId"].(float64); ok {
		tradeInfo.CourseId = int64(courseId)
	}
	if tradeId, ok := itemMap["tradeId"].(float64); ok {
		tradeInfo.TradeId = int64(tradeId)
	}
	if subTradeId, ok := itemMap["subTradeId"].(float64); ok {
		tradeInfo.SubTradeId = int64(subTradeId)
	}
	if tradeFee, ok := itemMap["tradeFee"].(float64); ok {
		tradeInfo.TradeFee = int64(tradeFee)
	}
	if tradeTime, ok := itemMap["tradeTime"].(float64); ok {
		tradeInfo.TradeTime = int64(tradeTime)
	}
	if refundStartTime, ok := itemMap["refundStartTime"].(float64); ok {
		tradeInfo.RefundStartTime = int64(refundStartTime)
	}
	if changeTime, ok := itemMap["changeTime"].(float64); ok {
		tradeInfo.ChangeTime = int64(changeTime)
	}
	if changeFromCourseId, ok := itemMap["changeFromCourseId"].(float64); ok {
		tradeInfo.ChangeFromCourseId = int64(changeFromCourseId)
	}
	if changeToCourseId, ok := itemMap["changeToCourseId"].(float64); ok {
		tradeInfo.ChangeToCourseId = int64(changeToCourseId)
	}
	if createTime, ok := itemMap["createTime"].(float64); ok {
		tradeInfo.CreateTime = int64(createTime)
	}
	if updateTime, ok := itemMap["updateTime"].(float64); ok {
		tradeInfo.UpdateTime = int64(updateTime)
	}
	if logInfo, ok := itemMap["logInfo"].(string); ok {
		tradeInfo.LogInfo = logInfo
	}

	// 解析订单状态
	var orderBusinessStatus, refundStatus int
	if obs, ok := itemMap["orderBusinessStatus"].(float64); ok {
		orderBusinessStatus = int(obs)
	}
	if rs, ok := itemMap["refundStatus"].(float64); ok {
		refundStatus = int(rs)
	}

	// 计算最终状态
	tradeInfo.Status = getTradeStatus(orderBusinessStatus, refundStatus)

	return tradeInfo, nil
}

// getTradeStatus 根据订单业务状态和售后状态获取dar订单状态
// 对应 PHP 的 Fudao_Dar::getTradeStatus
func getTradeStatus(orderBusinessStatus, refundStatus int) int {
	// 优先判断售后状态
	if refundStatus == RefundStatusRefunded {
		return TradeStatusRefunded
	}

	// 根据订单业务状态映射
	switch orderBusinessStatus {
	case OrderBusinessStatusUnpaid:
		return TradeStatusUnpaid
	case OrderBusinessStatusPartPaid:
		return TradeStatusUnpaid
	case OrderBusinessStatusPaid, OrderBusinessStatusFulfilling, OrderBusinessStatusToProduce,
		OrderBusinessStatusReceive, OrderBusinessStatusFinished, OrderBusinessStatusPause:
		return TradeStatusPaid
	default:
		return TradeStatusUnknown
	}
}
